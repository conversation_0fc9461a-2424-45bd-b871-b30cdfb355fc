'use client'

import { AppLayout } from '@/components/app-layout'
import { useTypedTranslation } from '@/hooks/useTypedTranslation'
import { useStoreInitialization } from '@/hooks/useStore'
import { DashboardOverview } from '@/components/dashboard/dashboard-overview'
import { DashboardToolbar } from '@/components/dashboard/dashboard-toolbar'
import { RecentRequests } from '@/components/dashboard/recent-requests'
import { TrendChart } from '@/components/dashboard/trend-chart'
import { useDashboardData } from '@/hooks/useDashboardData'

export default function DashboardPage() {
  const { t, isLoading } = useTypedTranslation('common')
  const { isHydrated } = useStoreInitialization()
  
  const {
    stats,
    loading,
    error
  } = useDashboardData()


  // 只有在翻译加载中或应用未水合时显示加载状态
  if (isLoading || !isHydrated) {
    return (
      <AppLayout title={t('dashboard')}>
        <div className="flex items-center justify-center h-full">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout title={t('dashboard')}>
      <div className="p-4 space-y-4">
        {/* 仪表盘统计和最近请求 */}
        <div className="space-y-4">
          <DashboardToolbar />
          
          <DashboardOverview
            stats={stats}
            loading={loading}
            error={error}
          />

          <div className="grid grid-cols-1 lg:grid-cols-10 gap-4">
            <TrendChart
              data={stats.requestTrends}
              loading={loading}
              error={error}
              className="lg:col-span-6"
            />
            
            <RecentRequests
              requests={stats.recentRequests}
              loading={loading}
              className="lg:col-span-4"
            />
          </div>
        </div>
      </div>
    </AppLayout>
  )
}