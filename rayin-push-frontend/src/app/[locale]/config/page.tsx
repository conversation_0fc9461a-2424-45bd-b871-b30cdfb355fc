'use client'

import { AppLayout } from '@/components/app-layout'
import { useTypedTranslation } from '@/hooks/useTypedTranslation'
import { useStoreInitialization } from '@/hooks/useStore'
import { ConfigList } from '@/components/config/config-list'
import { ConfigToolbar } from '@/components/config/config-toolbar'
import { ConfigEditModal } from '@/components/config/config-form'
import { useState } from 'react'
import { mockInterfaceConfigs } from '@/mock/configs'
import type { InterfaceConfig } from '@/types/data'

export default function ConfigPage() {
  const { t, isLoading } = useTypedTranslation('config')
  const { isHydrated } = useStoreInitialization()
  const [configs, setConfigs] = useState<InterfaceConfig[]>(mockInterfaceConfigs)
  const [selectedConfigs, setSelectedConfigs] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'enabled' | 'disabled'>('all')
  const [createModalOpen, setCreateModalOpen] = useState(false)

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // 过滤配置数据
  const filteredConfigs = configs.filter(config => {
    const matchesSearch = config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      config.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter === 'all' || config.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // 分页数据
  const total = filteredConfigs.length
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedConfigs = filteredConfigs.slice(startIndex, endIndex)

  // 处理配置状态切换
  const handleToggleStatus = (id: string) => {
    setConfigs(prev => prev.map(config =>
      config.id === id
        ? { ...config, status: config.status === 'enabled' ? 'disabled' : 'enabled' }
        : config
    ))
  }

  // 处理更新配置
  const handleUpdateConfig = (updatedConfig: InterfaceConfig) => {
    setConfigs(prev => prev.map(config =>
      config.id === updatedConfig.id ? updatedConfig : config
    ))
  }

  // 处理删除配置
  const handleDeleteConfig = (id: string) => {
    setConfigs(prev => prev.filter(config => config.id !== id))
    setSelectedConfigs(prev => prev.filter(configId => configId !== id))
  }

  // 处理创建配置
  const handleCreateConfig = (newConfig: InterfaceConfig) => {
    setConfigs(prev => [...prev, newConfig])
    setCreateModalOpen(false)
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    setConfigs(prev => prev.filter(config => !selectedConfigs.includes(config.id)))
    setSelectedConfigs([])
  }

  // 处理批量状态切换
  const handleBatchToggleStatus = (status: 'enabled' | 'disabled') => {
    setConfigs(prev => prev.map(config =>
      selectedConfigs.includes(config.id)
        ? { ...config, status }
        : config
    ))
    setSelectedConfigs([])
  }

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    setSelectedConfigs([]) // 切换页面时清空选择
  }

  // 处理每页数量变化
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize)
    setCurrentPage(1) // 重置到第一页
    setSelectedConfigs([]) // 清空选择
  }

  // 当搜索或筛选条件变化时重置到第一页
  const handleSearchChange = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
    setSelectedConfigs([])
  }

  const handleStatusFilterChange = (status: 'all' | 'enabled' | 'disabled') => {
    setStatusFilter(status)
    setCurrentPage(1)
    setSelectedConfigs([])
  }

  // 只有在翻译加载中或应用未水合时显示加载状态
  if (isLoading || !isHydrated) {
    return (
      <AppLayout title={t('title')}>
        <div className="flex items-center justify-center h-full">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout title={t('title')}>
      <div className="p-4 space-y-4">
        {/* 工具栏 */}
        <ConfigToolbar
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          statusFilter={statusFilter}
          onStatusFilterChange={handleStatusFilterChange}
          onCreateConfig={() => setCreateModalOpen(true)}
        />

        {/* 配置列表 */}
        <ConfigList
          configs={paginatedConfigs}
          selectedConfigs={selectedConfigs}
          onSelectionChange={setSelectedConfigs}
          onToggleStatus={handleToggleStatus}
          onDeleteConfig={handleDeleteConfig}
          onUpdateConfig={handleUpdateConfig}
          currentPage={currentPage}
          pageSize={pageSize}
          total={total}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          onBatchDelete={handleBatchDelete}
          onBatchToggleStatus={handleBatchToggleStatus}
        />

        {/* 创建配置模态框 */}
        <ConfigEditModal
          config={null}
          open={createModalOpen}
          onOpenChange={setCreateModalOpen}
          onSave={handleCreateConfig}
        />
      </div>
    </AppLayout>
  )
}