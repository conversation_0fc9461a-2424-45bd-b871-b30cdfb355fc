'use client'

import { AppLayout } from '@/components/app-layout'
import { useTypedTranslation } from '@/hooks/useTypedTranslation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileText } from 'lucide-react'
import { useStoreInitialization } from '@/hooks/useStore'

export default function LogsPage() {
  const { t, isLoading } = useTypedTranslation('common')
  const { isHydrated } = useStoreInitialization()

  if (isLoading || !isHydrated) {
    return (
      <AppLayout title={t('logs')}>
        <div className="flex items-center justify-center h-full">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout title={t('logs')}>
      <div className="p-4 space-y-4">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">{t('logs')}</h1>
        </div>

        {/* 占位内容 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>请求日志管理</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center py-12 text-muted-foreground">
              <div className="mb-4">
                <FileText className="h-16 w-16 mx-auto opacity-50" />
              </div>
              <h3 className="text-lg font-semibold mb-2">日志管理页面开发中</h3>
              <p className="mb-4">
                这个页面将在任务 12.1-12.4 中实现，包含日志列表、筛选、搜索和导出功能。
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}