{"welcome": "Welcome to <PERSON><PERSON>", "description": "Message push notification system", "dashboard": "Dashboard", "config": "Interface Config", "channels": "Notification Channels", "logs": "Request Logs", "limits": "Request Limits", "users": "User Management", "settings": "Settings", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "search": "Search", "create": "Create", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "failed": "Failed", "status": "Status", "actions": "Actions", "name": "Name", "time": "Time", "type": "Type", "management": "Management", "system": "System", "monitoring": "System Monitoring", "analytics": "Analytics", "webhooks": "Webhook Management", "database": "Database Status", "operate": "Operate", "updatedTime": "Updated Time", "createdTime": "Created Time"}