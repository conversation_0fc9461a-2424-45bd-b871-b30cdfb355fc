'use client'

import { useState } from 'react'
import { DashboardStats } from '@/types/data'
import { mockDashboardStats } from '@/mock/dashboard'

interface UseDashboardDataOptions {
  // Options removed for simplicity
}

interface UseDashboardDataReturn {
  stats: DashboardStats
  loading: boolean
  error: string | null
}

export function useDashboardData(
  _options: UseDashboardDataOptions = {}
): UseDashboardDataReturn {
  const [stats] = useState<DashboardStats>(mockDashboardStats)
  const [loading] = useState(false)
  const [error] = useState<string | null>(null)

  return {
    stats,
    loading,
    error
  }
}