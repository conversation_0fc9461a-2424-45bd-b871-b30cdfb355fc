'use client'

import { usePara<PERSON>, useRouter, usePathname } from 'next/navigation'
import { useState, useEffect } from 'react'

export function useLocale() {
  const params = useParams()
  const router = useRouter()
  const pathname = usePathname()
  
  const currentLocale = (params?.locale as string) || 'zh'
  
  const switchLocale = (newLocale: string) => {
    // 替换当前路径中的语言代码
    const segments = pathname.split('/')
    segments[1] = newLocale
    const newPath = segments.join('/')
    
    router.push(newPath)
  }
  
  return {
    locale: currentLocale,
    switchLocale,
    isZh: currentLocale === 'zh',
    isEn: currentLocale === 'en'
  }
}