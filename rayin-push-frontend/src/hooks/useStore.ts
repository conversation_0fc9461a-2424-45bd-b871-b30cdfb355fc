'use client'

import { useEffect, useState } from 'react'
import { useGlobalStore, useAuthStore, useAppStore } from '@/stores'
import { isStorageAvailable } from '@/lib/storage'

// Store初始化Hook
export function useStoreInitialization() {
  const [isHydrated, setIsHydrated] = useState(false)
  const setInitialized = useAppStore(state => state.setInitialized)
  const setError = useAppStore(state => state.setError)
  const setTheme = useGlobalStore(state => state.setTheme)
  const theme = useGlobalStore(state => state.theme)

  useEffect(() => {
    // 检查存储是否可用
    if (!isStorageAvailable()) {
      console.warn('LocalStorage is not available, using default values')
      setError('存储不可用，将使用默认设置')
    }

    // 应用主题
    const applyTheme = (themeValue: string) => {
      const root = window.document.documentElement
      root.classList.remove('light', 'dark')
      
      if (themeValue === 'system') {
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        root.classList.add(systemTheme)
      } else {
        root.classList.add(themeValue)
      }
    }

    // 初始化主题
    applyTheme(theme)

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleSystemThemeChange = () => {
      if (theme === 'system') {
        applyTheme('system')
      }
    }

    mediaQuery.addEventListener('change', handleSystemThemeChange)

    // 标记为已初始化
    setIsHydrated(true)
    setInitialized(true)

    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange)
    }
  }, [theme, setInitialized, setError, setTheme])

  return { isHydrated }
}

// 主题切换Hook
export function useTheme() {
  const theme = useGlobalStore(state => state.theme)
  const setTheme = useGlobalStore(state => state.setTheme)

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }

  const setSystemTheme = () => {
    setTheme('system')
  }

  return {
    theme,
    setTheme,
    toggleTheme,
    setSystemTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
    isSystem: theme === 'system',
  }
}

// 侧边栏状态Hook
export function useSidebar() {
  const sidebar = useGlobalStore(state => state.sidebar)
  const setSidebarOpen = useGlobalStore(state => state.setSidebarOpen)
  const setSidebarCollapsed = useGlobalStore(state => state.setSidebarCollapsed)
  const setSidebarMobile = useGlobalStore(state => state.setSidebarMobile)
  const toggleSidebar = useGlobalStore(state => state.toggleSidebar)
  const toggleSidebarCollapse = useGlobalStore(state => state.toggleSidebarCollapse)

  // 监听屏幕尺寸变化
  useEffect(() => {
    if (typeof window === 'undefined') return

    const checkMobile = () => {
      const isMobile = window.innerWidth < 768
      setSidebarMobile(isMobile)
      
      // 在移动端自动关闭侧边栏
      if (isMobile && sidebar.isOpen) {
        setSidebarOpen(false)
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => {
      window.removeEventListener('resize', checkMobile)
    }
  }, [setSidebarMobile, setSidebarOpen, sidebar.isOpen])

  return {
    ...sidebar,
    setSidebarOpen,
    setSidebarCollapsed,
    setSidebarMobile,
    toggleSidebar,
    toggleSidebarCollapse,
  }
}