'use client'

import { useEffect, useState, useMemo } from 'react'
import { useLocale } from './useLocale'
import type { 
  TranslationNamespace, 
  TranslationTypeMap, 
  SupportedLocale 
} from '@/types/i18n'

// 翻译缓存
const translationCache = new Map<string, any>()

export function useTypedTranslation<T extends TranslationNamespace>(
  namespace: T
): {
  t: (key: keyof TranslationTypeMap[T], fallback?: string) => string
  locale: SupportedLocale
  isLoading: boolean
} {
  const { locale } = useLocale()
  const [translations, setTranslations] = useState<TranslationTypeMap[T] | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadTranslations = async () => {
      const cacheKey = `${locale}/${namespace}`
      
      // 检查缓存
      if (translationCache.has(cacheKey)) {
        setTranslations(translationCache.get(cacheKey))
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        const translationModule = await import(`@/locales/${locale}/${namespace}.json`)
        const translations = translationModule.default || translationModule
        
        // 缓存翻译结果
        translationCache.set(cacheKey, translations)
        setTranslations(translations)
      } catch (error) {
        console.error(`Failed to load translations for ${locale}/${namespace}:`, error)
        setTranslations(null)
      } finally {
        setIsLoading(false)
      }
    }

    loadTranslations()
  }, [locale, namespace])

  const t = (key: keyof TranslationTypeMap[T], fallback?: string): string => {
    if (!translations) {
      return fallback || String(key)
    }
    return translations[key] as string || fallback || String(key)
  }

  return { 
    t, 
    locale: locale as SupportedLocale, 
    isLoading 
  }
}