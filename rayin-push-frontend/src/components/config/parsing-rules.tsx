'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import type { InterfaceConfig } from '@/types/data'
import { Plus, Trash2, Info } from 'lucide-react'

interface ParsingRulesProps {
  method: 'GET' | 'POST'
  rules: InterfaceConfig['parsingRules']
  onChange: (rules: InterfaceConfig['parsingRules']) => void
}

export function ParsingRules({ 
  method, 
  rules, 
  onChange 
}: ParsingRulesProps) {
  // 获取方法颜色（与ConfigList保持一致）
  const getMethodColor = (method: string) => {
    const colors = {
      'GET': 'bg-purple-50 text-purple-700 border border-purple-200',
      'POST': 'bg-blue-50 text-blue-700 border border-blue-200',
      'PUT': 'bg-orange-50 text-orange-700 border border-orange-200',
      'DELETE': 'bg-red-50 text-red-700 border border-red-200',
      'PATCH': 'bg-purple-50 text-purple-700 border border-purple-200'
    }
    return colors[method as keyof typeof colors] || 'bg-gray-50 text-gray-700 border border-gray-200'
  }

  // 当请求方式变化时自动更新规则类型
  const getRuleType = () => {
    if (method === 'GET') return 'get'
    return rules?.type === 'get' ? 'post-json' : rules?.type || 'post-json'
  }

  const currentRuleType = getRuleType()

  // 更新规则类型
  const handleTypeChange = (type: 'post-form' | 'post-multipart' | 'post-json' | 'post-plain') => {
    onChange({
      ...(rules || {}),
      type,
      variableMapping: type === 'post-plain' ? {} : (rules?.variableMapping || {}),
      regexPatterns: type === 'post-plain' ? (rules?.regexPatterns || {}) : undefined
    })
  }

  // 添加参数映射
  const handleAddMapping = () => {
    const newRules = { ...(rules?.variableMapping || {}) }
    const existingNumbers = Object.keys(newRules)
      .filter(key => key.startsWith('新参数'))
      .map(key => parseInt(key.replace('新参数', '')))
      .filter(num => !isNaN(num))
    const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1
    newRules[`新参数${nextNumber}`] = `变量${nextNumber}`
    onChange({ ...(rules || {}), variableMapping: newRules })
  }

  // 更新参数映射
  const handleMappingChange = (oldKey: string, newKey: string, value: string) => {
    const newRules = { ...(rules?.variableMapping || {}) }
    if (oldKey !== newKey) {
      delete newRules[oldKey]
    }
    newRules[newKey] = value
    onChange({ ...(rules || {}), variableMapping: newRules })
  }

  // 删除参数映射
  const handleDeleteMapping = (key: string) => {
    const newRules = { ...(rules?.variableMapping || {}) }
    delete newRules[key]
    onChange({ ...(rules || {}), variableMapping: newRules })
  }

  // 添加正则表达式规则
  const handleAddRegexPattern = () => {
    const newPatterns = { ...(rules?.regexPatterns || {}) }
    const existingNumbers = Object.keys(newPatterns)
      .filter(key => key.startsWith('变量'))
      .map(key => parseInt(key.replace('变量', '')))
      .filter(num => !isNaN(num))
    const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1
    newPatterns[`变量${nextNumber}`] = `正则表达式${nextNumber}`
    onChange({ ...(rules || {}), regexPatterns: newPatterns })
  }

  // 更新正则表达式规则
  const handleRegexPatternChange = (key: string, newKey: string, value: string) => {
    const newPatterns = { ...(rules?.regexPatterns || {}) }
    if (key !== newKey) {
      delete newPatterns[key]
    }
    newPatterns[newKey] = value
    onChange({ ...(rules || {}), regexPatterns: newPatterns })
  }

  // 删除正则表达式规则
  const handleDeleteRegexPattern = (key: string) => {
    const newPatterns = { ...(rules?.regexPatterns || {}) }
    delete newPatterns[key]
    onChange({ ...(rules || {}), regexPatterns: newPatterns })
  }

  return (
    <div className="space-y-6">
      {/* 请求类型说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            消息提取规则
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Label>请求方式</Label>
              <div className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium ${getMethodColor(method)}`}>
                {method}
              </div>
            </div>

            {method === 'POST' && (
              <div className="space-y-2">
                <Label>内容类型</Label>
                <Select 
                  value={currentRuleType} 
                  onValueChange={handleTypeChange}
                >
                  <SelectTrigger className="w-full cursor-pointer">
                    <SelectValue>
                      {currentRuleType === 'post-form' && 'application/x-www-form-urlencoded'}
                      {currentRuleType === 'post-multipart' && 'multipart/form-data'}
                      {currentRuleType === 'post-json' && 'application/json'}
                      {currentRuleType === 'post-plain' && 'text/plain'}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent className="max-w-md">
                    <SelectItem value="post-form">
                      <div className="max-w-xs">
                        <div>application/x-www-form-urlencoded</div>
                        <div className="text-xs text-muted-foreground">配置表单字段映射，将接口传递的字段名映射为模板中使用的变量名</div>
                      </div>
                    </SelectItem>
                    <SelectItem value="post-multipart">
                      <div className="max-w-xs">
                        <div>multipart/form-data</div>
                        <div className="text-xs text-muted-foreground">配置表单字段映射，将接口传递的字段名映射为模板中使用的变量名</div>
                      </div>
                    </SelectItem>
                    <SelectItem value="post-json">
                      <div className="max-w-xs">
                        <div>application/json</div>
                        <div className="text-xs text-muted-foreground">配置 json 字段路径映射，支持提取多层级的字段</div>
                      </div>
                    </SelectItem>
                    <SelectItem value="post-plain">
                      <div className="max-w-xs">
                        <div>text/plain</div>
                        <div className="text-xs text-muted-foreground">使用正则表达式从文本中提取数据，每个正则表达式提取一个变量</div>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 参数映射配置 (GET/POST-form/POST-json) */}
      {currentRuleType !== 'post-plain' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>
                变量映射
              </CardTitle>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddMapping}
              >
                <Plus className="h-4 w-4 mr-1" />
                添加映射
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {Object.keys(rules?.variableMapping || {}).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                暂无参数映射，点击&ldquo;添加映射&rdquo;开始配置
              </div>
            ) : (
              <div className="space-y-3">
                {Object.entries(rules?.variableMapping || {}).map(([key, value], index) => (
                  <div key={`${key}-${index}`} className="flex items-center gap-2">
                    <div className="flex-1">
                      <Input
                        placeholder={
                          currentRuleType === 'post-json' 
                            ? "JSON路径 (如: user.name, info.address)" 
                            : "接口参数名"
                        }
                        value={key}
                        onChange={(e) => handleMappingChange(key, e.target.value, value)}
                      />
                    </div>
                    <div className="text-muted-foreground">→</div>
                    <div className="flex-1">
                      <Input
                        placeholder="模板变量名"
                        value={value}
                        onChange={(e) => handleMappingChange(key, key, e.target.value)}
                      />
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteMapping(key)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 正则表达式配置 (POST-plain) */}
      {currentRuleType === 'post-plain' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>正则表达式规则</CardTitle>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddRegexPattern}
                className='cursor-pointer'
              >
                <Plus className="h-4 w-4 mr-1" />
                添加规则
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {!rules?.regexPatterns || Object.keys(rules.regexPatterns).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                暂无正则表达式规则，点击&ldquo;添加规则&rdquo;开始配置
              </div>
            ) : (
              <div className="space-y-4">
                {Object.entries(rules?.regexPatterns || {}).map(([key, pattern], index) => (
                  <div key={key} className="p-4 border rounded-lg space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="font-medium">规则 {index + 1}</Label>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteRegexPattern(key)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div className="space-y-2">
                        <Label>变量名</Label>
                        <Input
                          placeholder="模板中使用的变量名"
                          value={key}
                          onChange={(e) => 
                            handleRegexPatternChange(key, e.target.value, pattern)
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>正则表达式</Label>
                        <Input
                          placeholder="如: 我叫(.+)"
                          value={pattern}
                          onChange={(e) => 
                            handleRegexPatternChange(key, key, e.target.value)
                          }
                          className="font-mono text-sm"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}