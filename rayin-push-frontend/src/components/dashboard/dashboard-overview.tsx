'use client'

import { StatisticsCard } from './stats-card'
import { useTypedTranslation } from '@/hooks/useTypedTranslation'
import { DashboardStats } from '@/types/data'
import { Activity, TrendingUp, Clock } from 'lucide-react'

interface DashboardOverviewProps {
  stats: DashboardStats
  loading?: boolean
  error?: string | null
}

export function DashboardOverview({ stats, loading, error }: DashboardOverviewProps) {
  const { t } = useTypedTranslation('dashboard')

  // 计算趋势数据
  const calculateTrend = (current: number, previous: number) => {
    if (previous === 0) return { value: 0, isPositive: true }
    const change = ((current - previous) / previous) * 100
    return {
      value: Math.abs(change),
      isPositive: change >= 0
    }
  }

  // 基于历史趋势数据计算昨天的统计
  const getYesterdayStats = () => {
    const trends = stats.requestTrends
    if (trends.length < 2) {
      // 如果没有足够的历史数据，使用模拟数据
      return {
        totalRequests: Math.max(0, stats.totalRequests - Math.floor(Math.random() * 1000 + 100)),
        successRate: Math.max(0, Math.min(100, stats.successRate - (Math.random() * 10 - 5))),
        todayRequests: Math.max(0, Math.floor(stats.todayRequests * (0.7 + Math.random() * 0.4)))
      }
    }

    // 使用最近两天的数据计算趋势
    const today = trends[trends.length - 1]
    const yesterday = trends[trends.length - 2]
    
    const yesterdaySuccessRate = yesterday.requests > 0 
      ? (yesterday.success / yesterday.requests) * 100 
      : stats.successRate * 0.95

    return {
      totalRequests: Math.max(0, stats.totalRequests - (today.requests - yesterday.requests)),
      successRate: yesterdaySuccessRate,
      todayRequests: yesterday.requests
    }
  }

  const yesterdayStats = getYesterdayStats()

  const requestsTrend = calculateTrend(stats.totalRequests, yesterdayStats.totalRequests)
  const successRateTrend = calculateTrend(stats.successRate, yesterdayStats.successRate)
  const todayTrend = calculateTrend(stats.todayRequests, yesterdayStats.todayRequests)

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <StatisticsCard
        title={t('todayRequests')}
        value={stats.todayRequests}
        icon={<Clock className="h-4 w-4" />}
        trend={{
          ...todayTrend,
          timeframe: t('last24Hours')
        }}
        loading={loading}
        error={error}
      />
      
      <StatisticsCard
        title={t('totalRequests')}
        value={stats.totalRequests}
        icon={<Activity className="h-4 w-4" />}
        trend={{
          ...requestsTrend,
          timeframe: t('last24Hours')
        }}
        loading={loading}
        error={error}
      />
      
      <StatisticsCard
        title={t('successRate')}
        value={`${stats.successRate.toFixed(1)}%`}
        icon={<TrendingUp className="h-4 w-4" />}
        trend={{
          ...successRateTrend,
          timeframe: t('last24Hours')
        }}
        loading={loading}
        error={error}
      />
    </div>
  )
}