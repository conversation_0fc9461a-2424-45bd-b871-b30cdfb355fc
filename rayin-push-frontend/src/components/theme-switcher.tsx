'use client'

import { useTheme } from '@/hooks/useStore'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Moon, Sun, Monitor, Check } from 'lucide-react'

interface ThemeSwitchProps {
  compact?: boolean
  className?: string
}

export function ThemeSwitch({ compact = false, className }: ThemeSwitchProps) {
  const { setTheme, isDark, isLight, theme } = useTheme()

  const getIcon = () => {
    if (isDark) return <Moon className="h-4 w-4" />
    if (isLight) return <Sun className="h-4 w-4" />
    return <Monitor className="h-4 w-4" />
  }

  const getLabel = () => {
    if (isDark) return '深色'
    if (isLight) return '浅色'
    return '系统'
  }

  const cycleTheme = () => {
    if (isLight) {
      setTheme('dark')
    } else if (isDark) {
      setTheme('system')
    } else {
      setTheme('light')
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`h-8 w-8 p-0 ${className}`}
        >
          {getIcon()}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem 
          onClick={() => setTheme('light')} 
          className="flex items-center justify-between"
        >
          <div className="flex items-center">
            <Sun className="h-4 w-4 mr-2" />
            浅色
          </div>
          {theme === 'light' && <Check className="h-4 w-4" />}
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => setTheme('dark')} 
          className="flex items-center justify-between"
        >
          <div className="flex items-center">
            <Moon className="h-4 w-4 mr-2" />
            深色
          </div>
          {theme === 'dark' && <Check className="h-4 w-4" />}
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => setTheme('system')} 
          className="flex items-center justify-between"
        >
          <div className="flex items-center">
            <Monitor className="h-4 w-4 mr-2" />
            系统
          </div>
          {theme === 'system' && <Check className="h-4 w-4" />}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}