'use client'

import React from 'react'
import { Settings, <PERSON>lette, Globe, Bell, Key, Database, HelpCircle } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { useTypedTranslation } from '@/hooks/useTypedTranslation'
import { LanguageSwitch } from '@/components/language-switcher'
import { ThemeSwitch } from '@/components/theme-switcher'

interface SettingsMenuProps {
  className?: string
}

export function SettingsMenu({ className }: SettingsMenuProps) {
  const { t } = useTypedTranslation('common')

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className={`${className} cursor-pointer focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-transparent focus:ring-0 focus:ring-offset-0 focus:border-transparent`}>
          <Settings className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel>{t('settings')}</DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuGroup>
          <div className="px-2 py-1.5 text-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Globe className="mr-2 h-4 w-4" />
                <span>{t('language')}</span>
              </div>
              <LanguageSwitch compact />
            </div>
          </div>
          
          <div className="px-2 py-1.5 text-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Palette className="mr-2 h-4 w-4" />
                <span>{t('theme')}</span>
              </div>
              <ThemeSwitch compact />
            </div>
          </div>
        </DropdownMenuGroup>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuGroup>
          <DropdownMenuItem>
            <Bell className="mr-2 h-4 w-4" />
            <span>通知设置</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem>
            <Key className="mr-2 h-4 w-4" />
            <span>API 密钥</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem>
            <Database className="mr-2 h-4 w-4" />
            <span>数据备份</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem>
          <HelpCircle className="mr-2 h-4 w-4" />
          <span>帮助文档</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}