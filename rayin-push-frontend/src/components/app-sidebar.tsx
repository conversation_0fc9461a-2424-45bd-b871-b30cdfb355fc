'use client'

import { useMemo, useCallback, memo } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupContent,
  useSidebar,
} from '@/components/ui/sidebar'
import Image from 'next/image'
import { NavUser } from '@/components/layout/nav-user'

import { sidebarData } from '@/components/layout/data/sidebar-data'
import { navigationConfig, filterNavigationByRole } from '@/config/navigation'
import { useAuthStore } from '@/stores'
import { useTypedTranslation } from '@/hooks/useTypedTranslation'
import type { NavigationTranslations } from '@/types/i18n'

function AppSidebarComponent({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, isAuthenticated } = useAuthStore()
  const { t } = useTypedTranslation('navigation')
  const pathname = usePathname()
  const { state, isMobile, setOpenMobile } = useSidebar()

  // Use actual user data if available, otherwise use default
  const currentUser = user || sidebarData.user

  // 过滤导航项根据用户权限，使用 useMemo 缓存
  const filteredNavigation = useMemo(
    () => filterNavigationByRole(navigationConfig, user?.role, isAuthenticated),
    [user?.role, isAuthenticated]
  )

  // 扁平化所有导航项，移除分组
  const flatNavItems = useMemo(() => {
    const items: Array<{
      title: string
      url: string
      icon: any
    }> = []

    filteredNavigation.forEach((section) => {
      section.items.forEach((item) => {
        items.push({
          title: item.titleKey ? t(item.titleKey as keyof NavigationTranslations) : item.title,
          url: item.href,
          icon: item.icon,
        })
      })
    })

    return items
  }, [filteredNavigation, t])

  // 获取当前语言代码
  const currentLocale = useMemo(() => {
    const localeMatch = pathname.match(/^\/([a-z]{2})/)
    return localeMatch ? localeMatch[1] : 'zh' // 默认为中文
  }, [pathname])

  // 检查路径是否匹配当前路由
  const isActiveRoute = useCallback((url: string) => {
    // 移除语言前缀来比较路径
    const currentPath = pathname.replace(/^\/[a-z]{2}/, '') || '/'
    const targetPath = url === '/' ? '/' : url
    return currentPath === targetPath || currentPath.startsWith(targetPath + '/')
  }, [pathname])

  // 为URL添加语言前缀
  const getLocalizedUrl = useCallback((url: string) => {
    return `/${currentLocale}${url}`
  }, [currentLocale])

  // 处理导航点击
  const handleNavClick = useCallback((e: React.MouseEvent, url: string) => {
    // 检查是否是当前页面
    const currentPath = pathname.replace(/^\/[a-z]{2}/, '') || '/'
    const targetPath = url === '/' ? '/' : url

    // 如果是当前页面，不需要加载
    if (currentPath === targetPath) {
      e.preventDefault()
      return
    }

    // 在移动端点击导航项后关闭侧边栏
    if (isMobile) {
      setOpenMobile(false)
    }
    // 阻止事件冒泡，防止触发侧边栏切换
    e.stopPropagation()
  }, [pathname, isMobile, setOpenMobile])

  return (
    <Sidebar collapsible="icon" variant="floating" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="flex items-center gap-2 px-2 py-2 cursor-default">
              <div className="flex aspect-square size-8 items-center justify-center">
                <Image
                  src="/logo.svg"
                  alt="Rayin Push Logo"
                  width={28}
                  height={28}
                />
              </div>
              <div className="grid flex-1 text-left leading-tight">
                <span className="truncate font-semibold text-lg bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                  Rayin Push
                </span>
              </div>
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {flatNavItems.map((item, index) => (
                <SidebarMenuItem key={`${item.url}-${index}`}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActiveRoute(item.url)}
                    tooltip={state === 'collapsed' ? item.title : undefined}
                  >
                    <Link
                      href={getLocalizedUrl(item.url)}
                      onClick={(e) => handleNavClick(e, item.url)}
                    >
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={currentUser} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

export const AppSidebar = memo(AppSidebarComponent)