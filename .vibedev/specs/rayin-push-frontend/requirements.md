# Rayin-Push Frontend Requirements

## 项目简介

Rayin-Push 是一个 Webhook 转发管理系统的前端界面，旨在为用户提供统一的多渠道消息推送管理平台。系统基于 Next.js + Shadcn + Zustand 技术栈构建，支持国际化和响应式设计，采用现代化后台管理界面风格。

## 功能需求

### 1. 仪表盘模块

**用户故事**: 作为系统用户，我希望在仪表盘中查看系统整体运行状况和关键指标，以便快速了解系统使用情况和性能表现。

**验收标准**:
1. 系统应显示用户数量统计卡片
2. 系统应显示今日请求数量统计卡片
3. 系统应显示总请求数量统计卡片
4. 系统应显示成功率百分比统计卡片
5. 系统应显示接口数量统计卡片
6. 系统应显示最近10个请求的列表，包含请求时间、接口名称、状态
7. 系统应支持点击请求列表项跳转到请求日志详情页面
8. 系统应显示最近请求趋势图表，支持选择时间范围（24小时、7天、30天）
9. 所有统计数据应自动刷新或支持手动刷新

### 2. 接口配置管理模块

**用户故事**: 作为系统管理员，我希望能够创建和管理 webhook 接口配置，以便为不同的业务场景提供个性化的消息转发服务。

**验收标准**:
1. 系统应显示接口配置列表，包含配置名称、token、描述、状态、推送渠道、创建时间、更新时间
2. 系统应支持创建新的接口配置
3. 系统应为每个配置项提供操作按钮：测试推送、编辑、删除、禁用/启用
4. 系统应支持编辑接口配置的基本信息
5. 系统应支持配置消息提取规则，包括：
   - GET 请求的 URL 参数映射配置
   - POST 请求的不同内容类型支持：x-www-form-urlencoded、form-data、JSON、plain text
   - JSON 数据的嵌套字段提取配置（如 info.address -> userAddress）
   - Plain text 的正则表达式解析规则配置
6. 系统应支持测试推送功能验证配置正确性
7. 系统应支持软删除和恢复配置
8. 系统应显示配置的使用统计和状态

### 3. 通知渠道模块

**用户故事**: 作为系统用户，我希望能够配置多种通知渠道，以便将消息推送到不同的第三方平台。

**验收标准**:
1. 系统应支持创建通知渠道配置
2. 系统应提供内置模板：微信机器人、飞书机器人
3. 系统应支持自定义 Webhook 配置
4. 系统应支持多种请求方式：GET（Query参数）、POST（JSON、plain、x-www-form-urlencoded、form-data）
5. 系统应支持模板变量替换，包括：
   - 从接口配置提取的变量 ${xxx}
   - 内置变量：${data}、${now}
   - 简单运算：${x1 + x2}
   - 条件判断：if/else 语句
6. 系统应支持测试通知渠道配置
7. 系统应显示渠道配置列表和状态
8. 系统应支持编辑和删除渠道配置

### 4. 请求日志模块

**用户故事**: 作为系统用户，我希望能够查看详细的请求日志和推送结果，以便监控系统运行状况和排查问题。

**验收标准**:
1. 系统应显示请求日志列表，包含接口名称、状态、耗时、请求时间、错误信息
2. 系统应支持按时间范围、状态、接口名称筛选日志
3. 系统应支持点击日志条目查看详细信息
4. 系统应在日志详情中显示给各个渠道发送的消息结果列表
5. 系统应显示请求的原始数据和处理后的数据
6. 系统应支持导出日志数据
7. 系统应支持日志搜索功能
8. 系统应分页显示日志列表

### 5. 请求限制模块

**用户故事**: 作为系统管理员，我希望能够设置请求限制规则，以便保护系统免受恶意攻击和过量请求。

**验收标准**:
1. 系统应支持配置速率限制规则，包含时间窗口（分钟）和最大请求数
2. 系统应支持配置多条速率限制规则
3. 系统应使用滑动窗口算法实现速率限制
4. 系统应支持配置 IP 访问限制，包括单个 IP 和 IP 段
5. 系统应支持 IP 白名单和黑名单配置
6. 系统应显示当前生效的限制规则列表
7. 系统应支持启用/禁用限制规则
8. 系统应显示触发限制的统计信息
9. 系统应支持编辑和删除限制规则

### 6. 用户管理模块

**用户故事**: 作为系统管理员，我希望能够管理系统用户和权限，以便确保系统的安全性和访问控制。

**验收标准**:
1. 系统应仅对管理员角色显示用户管理模块
2. 系统应支持创建新用户，包含用户名、邮箱、密码字段
3. 系统应支持分配用户角色：admin（管理员）和 user（普通用户）
4. 系统应支持编辑用户信息（除第一个管理员外）
5. 系统应支持重置用户密码功能
6. 系统应支持启用/禁用用户账户
7. 系统应显示用户列表，包含创建时间、最后登录时间
8. 系统应保护第一个注册的管理员账户不被删除
9. 系统应阻止管理员删除自己的账户
10. 系统应记录用户操作日志

## 非功能性需求

### 7. 国际化支持

**用户故事**: 作为国际用户，我希望系统支持多语言界面，以便更好地理解和使用系统功能。

**验收标准**:
1. 系统应支持中文和英文界面切换
2. 系统应使用 i18next 库实现国际化
3. 系统应保存用户的语言偏好设置
4. 系统应翻译所有用户界面文本
5. 系统应支持数字、日期、时间的本地化格式

### 8. 响应式设计

**用户故事**: 作为移动设备用户，我希望能够在不同设备上正常使用系统，以便随时随地管理 webhook 配置。

**验收标准**:
1. 系统应适配桌面端（1920px+）、平板端（768px-1919px）、移动端（<768px）
2. 系统应在移动端提供友好的导航体验
3. 系统应确保所有功能在移动端可正常使用
4. 系统应优化移动端的表格和表单显示
5. 系统应支持触摸操作和手势

### 9. 技术架构

**用户故事**: 作为开发者，我希望系统具有良好的技术架构，以便后续维护和扩展。

**验收标准**:
1. 系统应使用 Next.js 14+ 作为 React 框架
2. 系统应使用 Shadcn/ui 作为 UI 组件库
3. 系统应使用 Zustand 进行状态管理
4. 系统应使用 TailwindCSS 进行样式管理
5. 系统应使用 pnpm 作为包管理器
6. 系统应包含 TypeScript 类型定义
7. 系统应具有清晰的目录结构
8. 系统应包含必要的开发工具配置（ESLint、Prettier）

### 10. 数据模拟

**用户故事**: 作为前端开发者，我希望有充足的模拟数据来展示系统功能，以便完整测试用户界面和交互流程。

**验收标准**:
1. 系统应包含仪表盘统计数据的模拟
2. 系统应包含接口配置的示例数据
3. 系统应包含通知渠道的示例配置
4. 系统应包含请求日志的历史记录模拟
5. 系统应包含用户数据的示例
6. 系统应模拟各种状态和场景（成功、失败、待处理等）
7. 模拟数据应具有真实性和多样性